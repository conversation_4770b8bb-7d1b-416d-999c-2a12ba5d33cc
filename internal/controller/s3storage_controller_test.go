/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package controller

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	s3managerv1 "gitlab.mtk.zone/mt-public/s3manager/api/v1"
)

func TestS3StorageController(t *testing.T) {
	// Create a fake client and context for testing
	scheme := runtime.NewScheme()
	err := s3managerv1.AddToScheme(scheme)
	assert.NoError(t, err)
	k8sClient := fake.NewClientBuilder().WithScheme(scheme).Build()
	ctx := context.Background()

	metadata := metav1.ObjectMeta{
		Name:      "test",
		Namespace: "default",
	}
	s3StorageResource := &s3managerv1.S3Storage{
		ObjectMeta: metadata,
		Spec: s3managerv1.S3StorageSpec{
			Provider: "ovh",
			Region:   "eu-west-par",
		},
	}

	t.Run("Create a new S3Storage resource", func(t *testing.T) {
		assert.NoError(t, k8sClient.Create(ctx, s3StorageResource))

		// Verify the resource was created
		var createdResource s3managerv1.S3Storage
		err := k8sClient.Get(ctx, client.ObjectKey{
			Name:      "test",
			Namespace: "default",
		}, &createdResource)
		assert.NoError(t, err)
		assert.Equal(t, "ovh1", createdResource.Spec.Provider)
		assert.Equal(t, "eu-west-par", createdResource.Spec.Region)
	})
}

// var _ = Describe("S3Storage Controller", func() {
// 	Context("When reconciling a resource", func() {
// 		const resourceName = "test-resource"

// 		ctx := context.Background()

// 		typeNamespacedName := types.NamespacedName{
// 			Name:      resourceName,
// 			Namespace: "default", // TODO(user):Modify as needed
// 		}
// 		s3storage := &s3managerv1.S3Storage{}

// 		BeforeEach(func() {
// 			By("creating the custom resource for the Kind S3Storage")
// 			err := k8sClient.Get(ctx, typeNamespacedName, s3storage)
// 			if err != nil && errors.IsNotFound(err) {
// 				resource := &s3managerv1.S3Storage{
// 					ObjectMeta: metav1.ObjectMeta{
// 						Name:      resourceName,
// 						Namespace: "default",
// 					},
// 					Spec: s3managerv1.S3StorageSpec{
// 						Provider: "ovh",
// 						Region:   "eu-west-par",
// 					},
// 				}
// 				Expect(k8sClient.Create(ctx, resource)).To(Succeed())
// 			}
// 		})

// 		AfterEach(func() {
// 			// TODO(user): Cleanup logic after each test, like removing the resource instance.
// 			resource := &s3managerv1.S3Storage{}
// 			err := k8sClient.Get(ctx, typeNamespacedName, resource)
// 			Expect(err).NotTo(HaveOccurred())

// 			By("Cleanup the specific resource instance S3Storage")
// 			Expect(k8sClient.Delete(ctx, resource)).To(Succeed())
// 		})
// 		It("should successfully reconcile the resource", func() {
// 			By("Reconciling the created resource")
// 			controllerReconciler := &S3StorageReconciler{
// 				Client: k8sClient,
// 				Scheme: k8sClient.Scheme(),
// 			}

// 			_, err := controllerReconciler.Reconcile(ctx, reconcile.Request{
// 				NamespacedName: typeNamespacedName,
// 			})
// 			Expect(err).NotTo(HaveOccurred())
// 			// TODO(user): Add more specific assertions depending on your controller's reconciliation logic.
// 			// Example: If you expect a certain status condition after reconciliation, verify it here.
// 		})
// 	})
// })
